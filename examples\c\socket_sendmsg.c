// SPDX-License-Identifier: (LGPL-2.1 OR BSD-2-Clause)
#include <stdio.h>
#include <unistd.h>
#include <signal.h>
#include <string.h>
#include <errno.h>
#include <sys/resource.h>
#include <bpf/libbpf.h>
#include <bpf/bpf.h>
#include <arpa/inet.h>
#include "socket_sendmsg.h"
#include "socket_sendmsg.skel.h"

static volatile sig_atomic_t stop;

static void sig_int(int signo)
{
    stop = 1;
}

static int libbpf_print_fn(enum libbpf_print_level level, const char *format, va_list args)
{
    return vfprintf(stderr, format, args);
}

static void print_hex_data(const __u8 *data, __u32 len)
{
    printf("Data (%u bytes): ", len);
    for (__u32 i = 0; i < len && i < 32; i++) {
        printf("%02x ", data[i]);
    }
    if (len > 32) {
        printf("...");
    }
    printf("\n");
}

static void print_address(const struct sendmsg_event *e)
{
    if (e->family == AF_INET && e->addr_len >= 4) {
        struct in_addr addr;
        memcpy(&addr, e->addr, sizeof(addr));
        printf("IPv4 Address: %s:%u\n", inet_ntoa(addr), e->port);
    } else if (e->family == AF_INET6 && e->addr_len >= 16) {
        char addr_str[INET6_ADDRSTRLEN];
        if (inet_ntop(AF_INET6, e->addr, addr_str, sizeof(addr_str))) {
            printf("IPv6 Address: [%s]:%u\n", addr_str, e->port);
        }
    } else if (e->family != 0) {
        printf("Address Family: %u, Port: %u\n", e->family, e->port);
        if (e->addr_len > 0) {
            printf("Raw Address: ");
            for (__u32 i = 0; i < e->addr_len && i < MAX_ADDR_SIZE; i++) {
                printf("%02x ", e->addr[i]);
            }
            printf("\n");
        }
    }
}

static int handle_event(void *ctx, void *data, size_t data_sz)
{
    const struct sendmsg_event *e = data;

    printf("=== UDP DNS Query Detected ===\n");
    printf("Process: %s (PID: %u, TGID: %u)\n", e->comm, e->pid, e->tgid);

    print_address(e);

    if (e->data_len > 0) {
        printf("DNS Query Data (%u bytes):\n", e->data_len);
        print_hex_data(e->data, e->data_len);

        // Try to extract DNS query name if possible
        if (e->data_len >= 12) { // Minimum DNS header size
            printf("DNS Header Info:\n");
            printf("  Transaction ID: 0x%02x%02x\n", e->data[0], e->data[1]);
            printf("  Flags: 0x%02x%02x\n", e->data[2], e->data[3]);
            printf("  Questions: %u\n", (e->data[4] << 8) | e->data[5]);
            printf("  Answers: %u\n", (e->data[6] << 8) | e->data[7]);
        }
    }

    printf("Note: Check kernel trace for detailed packet data using:\n");
    printf("      sudo cat /sys/kernel/debug/tracing/trace_pipe\n");
    printf("\n");
    return 0;
}

int main(int argc, char **argv)
{
    struct socket_sendmsg_bpf *skel;
    struct ring_buffer *rb = NULL;
    int err;

    /* Set up libbpf errors and debug info callback */
    libbpf_set_print(libbpf_print_fn);

    /* Handle Ctrl-C */
    signal(SIGINT, sig_int);
    signal(SIGTERM, sig_int);

    /* Open, load, and verify BPF application */
    skel = socket_sendmsg_bpf__open_and_load();
    if (!skel) {
        fprintf(stderr, "Failed to open and load BPF skeleton\n");
        return 1;
    }

    /* Attach LSM handler */
    err = socket_sendmsg_bpf__attach(skel);
    if (err) {
        fprintf(stderr, "Failed to attach BPF skeleton: %d\n", err);
        goto cleanup;
    }

    /* Set up ring buffer polling */
    rb = ring_buffer__new(bpf_map__fd(skel->maps.rb), handle_event, NULL, NULL);
    if (!rb) {
        err = -1;
        fprintf(stderr, "Failed to create ring buffer\n");
        goto cleanup;
    }

    printf("Successfully started! Monitoring UDP DNS queries (port 53)...\n");
    printf("Process information will be shown here, packet data will be in kernel trace.\n");
    printf("To see detailed packet data, run in another terminal:\n");
    printf("  sudo cat /sys/kernel/debug/tracing/trace_pipe\n");
    printf("Press Ctrl-C to stop.\n\n");

    /* Process events */
    while (!stop) {
        err = ring_buffer__poll(rb, 100 /* timeout, ms */);
        /* Ctrl-C will cause -EINTR */
        if (err == -EINTR) {
            err = 0;
            break;
        }
        if (err < 0) {
            printf("Error polling ring buffer: %d\n", err);
            break;
        }
    }

cleanup:
    ring_buffer__free(rb);
    socket_sendmsg_bpf__destroy(skel);
    return -err;
}
