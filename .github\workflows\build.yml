name: libbpf-bootstrap build

on:
  push:
    branches:
      - master
  pull_request:
  schedule:
    - cron: '7 17 * * *'

jobs:
  build_libbpf_bootstrap:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        llvm: [11, 12, 13, 14, 15, 16, 17, 18, 19, 20]
    steps:
    - uses: actions/checkout@v2
      with:
        submodules: recursive
    - name: Build container (20.04)
      if: ${{ matrix.llvm < 14 }}
      uses: docker/build-push-action@v3
      with:
        push: false
        build-args: |
          LLVM_VERSION=${{ matrix.llvm }}
          VERSION=20.04
          SHORTNAME=focal
        file: ./.github/docker/Dockerfile.ubuntu
        tags: build_container
    - name: Build container (22.04)
      if: ${{ matrix.llvm >= 14 }}
      uses: docker/build-push-action@v3
      with:
        push: false
        build-args: |
          LLVM_VERSION=${{ matrix.llvm }}
          VERSION=22.04
          SHORTNAME=jammy
        file: ./.github/docker/Dockerfile.ubuntu
        tags: build_container
    - name: Build examples/c -- GNU Make
      run: |
        docker run \
          -v $(pwd):/libbpf-bootstrap \
          build_container \
          /bin/bash -c \
          'cd /libbpf-bootstrap/examples/c && make -j`nproc`'
    - name: Build examples/c -- CMake
      run: |
        docker run \
          -v $(pwd):/libbpf-bootstrap \
          build_container \
          /bin/bash -c \
          'cd /libbpf-bootstrap/examples/c && cmake ./ && make'
    - name: Build examples/rust
      run: |
        docker run \
          -v $(pwd):/libbpf-bootstrap \
          build_container \
          /bin/bash -c \
          'cd /libbpf-bootstrap/examples/rust && cargo build'
