// SPDX-License-Identifier: GPL-2.0 OR BSD-3-Clause
#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>
#include <bpf/bpf_core_read.h>
#include "socket_sendmsg.h"

char LICENSE[] SEC("license") = "GPL";

struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, 256 * 1024);
} rb SEC(".maps");

// Structure to store DNS connection info (IPv4 only)
struct dns_conn_info {
    __u16 family;
    __u16 port;
    __u8 addr[4]; // IPv4 address only
};

// Map to store connected UDP sockets to DNS servers
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 1024);
    __type(key, struct sock *);
    __type(value, struct dns_conn_info);
} udp_dns_connections SEC(".maps");

static __always_inline int extract_sockaddr_info(struct sockaddr *addr, 
                                                 struct sendmsg_event *event)
{
    if (!addr)
        return 0;

    __u16 family;
    if (bpf_probe_read_kernel(&family, sizeof(family), &addr->sa_family) != 0)
        return 0;

    // Only handle IPv4, skip IPv6
    if (family == AF_INET6)
        return 0;

    event->family = family;

    if (family == AF_INET) {
        struct sockaddr_in *addr_in = (struct sockaddr_in *)addr;
        __u32 sin_addr;
        __u16 sin_port;
        
        if (bpf_probe_read_kernel(&sin_addr, sizeof(sin_addr), &addr_in->sin_addr.s_addr) == 0) {
            // Copy sin_addr to event->addr manually (unroll loop for eBPF)
            if (sizeof(sin_addr) >= 1 && MAX_ADDR_SIZE >= 1) event->addr[0] = ((char*)&sin_addr)[0];
            if (sizeof(sin_addr) >= 2 && MAX_ADDR_SIZE >= 2) event->addr[1] = ((char*)&sin_addr)[1];
            if (sizeof(sin_addr) >= 3 && MAX_ADDR_SIZE >= 3) event->addr[2] = ((char*)&sin_addr)[2];
            if (sizeof(sin_addr) >= 4 && MAX_ADDR_SIZE >= 4) event->addr[3] = ((char*)&sin_addr)[3];
            event->addr_len = sizeof(sin_addr);
        }
        
        if (bpf_probe_read_kernel(&sin_port, sizeof(sin_port), &addr_in->sin_port) == 0) {
            event->port = __builtin_bswap16(sin_port); // Convert from network byte order
        }
    }

    return 0;
}

static __always_inline int extract_and_print_udp_data(struct iov_iter *iter,
                                                     struct sendmsg_event *event)
{
    if (!iter)
        return 0;

    // Try to extract DNS query data from iov_iter
    struct iovec *iov = NULL;

    // Try to get iovec pointer from iter (offset-based approach)
    if (bpf_probe_read_kernel(&iov, sizeof(iov), (void*)iter + 8) == 0 && iov) {
        struct iovec first_iov;
        if (bpf_probe_read_kernel(&first_iov, sizeof(first_iov), iov) == 0) {
            // Validate iovec data
            if (first_iov.iov_base && first_iov.iov_len >= 12 && first_iov.iov_len <= 512) {
                __u8 dns_data[32]; // Smaller buffer to avoid verifier issues
                __u32 copy_len = first_iov.iov_len;
                if (copy_len > 32) copy_len = 32;

                if (bpf_probe_read_user(dns_data, copy_len, first_iov.iov_base) == 0) {
                    // Parse DNS header and first label
                    if (copy_len >= 16) {
                        __u16 txid = (dns_data[0] << 8) | dns_data[1];
                        bpf_printk("DNS Query TxID: 0x%04x", txid);

                        // Try to extract domain name (first 8 chars only for safety)
                        if (copy_len >= 20) {
                            __u8 label_len = dns_data[12]; // First label length
                            if (label_len > 0 && label_len <= 8) {
                                char domain_part[9] = {};

                                // Copy first label characters (only first 8 chars, fixed indices)
                                domain_part[0] = dns_data[13];
                                domain_part[1] = dns_data[14];
                                domain_part[2] = dns_data[15];
                                domain_part[3] = dns_data[16];
                                domain_part[4] = dns_data[17];
                                domain_part[5] = dns_data[18];
                                domain_part[6] = dns_data[19];
                                domain_part[7] = dns_data[20];
                                domain_part[8] = '\0';

                                bpf_printk("DNS Query for: %s", domain_part);
                            } else {
                                bpf_printk("DNS Query (len=%u)", label_len);
                            }
                        } else {
                            bpf_printk("DNS Query (short packet)");
                        }
                    }

                    // Store first few bytes for userspace
                    if (copy_len >= 16) {
                        event->data[0] = dns_data[0];  event->data[1] = dns_data[1];
                        event->data[2] = dns_data[2];  event->data[3] = dns_data[3];
                        event->data[4] = dns_data[4];  event->data[5] = dns_data[5];
                        event->data[6] = dns_data[6];  event->data[7] = dns_data[7];
                        event->data[8] = dns_data[8];  event->data[9] = dns_data[9];
                        event->data[10] = dns_data[10]; event->data[11] = dns_data[11];
                        event->data[12] = dns_data[12]; event->data[13] = dns_data[13];
                        event->data[14] = dns_data[14]; event->data[15] = dns_data[15];
                        event->data_len = 16;
                    }
                }
            }
        }
    }

    return 0;
}

SEC("lsm/socket_sendmsg")
int BPF_PROG(socket_sendmsg_monitor, struct socket *sock, struct msghdr *msg, int size, int ret)
{
    /* ret is the return value from the previous BPF program
     * or 0 if it's the first hook.
     */
    if (ret != 0)
        return ret;

    struct sendmsg_event *event;
    __u64 pid_tgid;
    char comm[16];
    bool is_udp_dns = false;

    /* Get process information first for filtering */
    pid_tgid = bpf_get_current_pid_tgid();
    bpf_get_current_comm(&comm, sizeof(comm));

    /* Check if this is a UDP socket */
    if (sock) {
        struct sock *sk = BPF_CORE_READ(sock, sk);
        if (sk) {
            __u16 family = BPF_CORE_READ(sk, __sk_common.skc_family);
            __u8 protocol = BPF_CORE_READ(sk, sk_protocol);

            // Only process UDP packets
            if (protocol == IPPROTO_UDP) {
                /* First check if address is in msghdr */
                if (msg) {
                    struct sockaddr *msg_name;
                    if (bpf_probe_read_kernel(&msg_name, sizeof(msg_name), &msg->msg_name) == 0 && msg_name) {
                        __u16 addr_family;
                        if (bpf_probe_read_kernel(&addr_family, sizeof(addr_family), &msg_name->sa_family) == 0) {
                            if (addr_family == AF_INET) {
                                struct sockaddr_in *addr_in = (struct sockaddr_in *)msg_name;
                                __u16 port;
                                if (bpf_probe_read_kernel(&port, sizeof(port), &addr_in->sin_port) == 0) {
                                    // Check if destination port is 53 (DNS)
                                    if (__builtin_bswap16(port) == 53) {
                                        is_udp_dns = true;
                                        bpf_printk("UDP DNS Query detected from %s (PID: %u)", comm, pid_tgid & 0xffffffff);
                                    }
                                }
                            } else if (addr_family == AF_INET6) {
                                // Skip IPv6
                                return 0;
                            }
                        }
                    } else {
                        /* No address in msghdr, check if this socket is connected to DNS */
                        struct dns_conn_info *conn_info = bpf_map_lookup_elem(&udp_dns_connections, &sk);
                        if (conn_info) {
                            is_udp_dns = true;
                            bpf_printk("UDP DNS Query (connected) detected from %s (PID: %u)", comm, pid_tgid & 0xffffffff);
                        }
                    }
                }
            }
        }
    }

    /* Only process UDP DNS packets (port 53) */
    if (!is_udp_dns)
        return 0;

    /* Reserve space in ring buffer */
    event = bpf_ringbuf_reserve(&rb, sizeof(*event), 0);
    if (!event)
        return 0;

    /* Initialize event structure - zero out key fields */
    event->pid = 0;
    event->tgid = 0;
    event->family = 0;
    event->port = 0;
    event->addr_len = 0;
    event->data_len = 0;

    /* Fill process information */
    event->pid = pid_tgid & 0xffffffff;
    event->tgid = pid_tgid >> 32;
    // Copy comm manually (unroll for eBPF)
    event->comm[0] = comm[0];
    event->comm[1] = comm[1];
    event->comm[2] = comm[2];
    event->comm[3] = comm[3];
    event->comm[4] = comm[4];
    event->comm[5] = comm[5];
    event->comm[6] = comm[6];
    event->comm[7] = comm[7];
    event->comm[8] = comm[8];
    event->comm[9] = comm[9];
    event->comm[10] = comm[10];
    event->comm[11] = comm[11];
    event->comm[12] = comm[12];
    event->comm[13] = comm[13];
    event->comm[14] = comm[14];
    event->comm[15] = comm[15];

    /* Extract address and data information from msghdr */
    if (msg) {
        struct sockaddr *msg_name;
        struct iov_iter msg_iter;
        bool found_address = false;

        /* First try to get address from msghdr */
        if (bpf_probe_read_kernel(&msg_name, sizeof(msg_name), &msg->msg_name) == 0 && msg_name) {
            extract_sockaddr_info(msg_name, event);
            found_address = true;
        }

        /* If no address in msghdr, check if this socket was connected to DNS */
        if (!found_address && sock) {
            struct sock *sk = BPF_CORE_READ(sock, sk);
            if (sk) {
                struct dns_conn_info *conn_info = bpf_map_lookup_elem(&udp_dns_connections, &sk);
                if (conn_info) {
                    /* Use the stored connection address - IPv4 only */
                    event->family = conn_info->family;
                    event->port = conn_info->port;

                    // Copy IPv4 address (4 bytes)
                    event->addr[0] = conn_info->addr[0];
                    event->addr[1] = conn_info->addr[1];
                    event->addr[2] = conn_info->addr[2];
                    event->addr[3] = conn_info->addr[3];
                    event->addr_len = 4;

                    found_address = true;
                    bpf_printk("Using stored DNS connection address");
                }
            }
        }

        /* Extract data if we found a valid DNS connection */
        if (found_address && bpf_probe_read_kernel(&msg_iter, sizeof(msg_iter), &msg->msg_iter) == 0) {
            extract_and_print_udp_data(&msg_iter, event);
        }
    }

    /* Submit event to user space */
    bpf_ringbuf_submit(event, 0);

    /* Allow the operation to proceed */
    return 0;
}

SEC("lsm/socket_connect")
int BPF_PROG(socket_connect_monitor, struct socket *sock, struct sockaddr *address, int addrlen, int ret)
{
    /* ret is the return value from the previous BPF program
     * or 0 if it's the first hook.
     */
    if (ret != 0)
        return ret;

    if (!sock || !address)
        return 0;

    struct sock *sk = BPF_CORE_READ(sock, sk);
    if (!sk)
        return 0;

    /* Check if this is a UDP socket */
    __u8 protocol = BPF_CORE_READ(sk, sk_protocol);
    if (protocol != IPPROTO_UDP)
        return 0;

    /* Check if connecting to port 53 (DNS) - IPv4 only */
    __u16 family;
    if (bpf_probe_read_kernel(&family, sizeof(family), &address->sa_family) != 0)
        return 0;

    // Skip IPv6
    if (family == AF_INET6)
        return 0;

    bool is_dns_port = false;
    if (family == AF_INET) {
        struct sockaddr_in *addr_in = (struct sockaddr_in *)address;
        __u16 port;
        if (bpf_probe_read_kernel(&port, sizeof(port), &addr_in->sin_port) == 0) {
            if (__builtin_bswap16(port) == 53) {
                is_dns_port = true;
            }
        }
    }

    if (is_dns_port) {
        /* Store the connection information in the map - IPv4 only */
        struct dns_conn_info conn_info = {};
        conn_info.family = family;

        struct sockaddr_in *addr_in = (struct sockaddr_in *)address;
        __u32 sin_addr;
        __u16 sin_port;

        if (bpf_probe_read_kernel(&sin_addr, sizeof(sin_addr), &addr_in->sin_addr.s_addr) == 0) {
            // Copy IPv4 address (4 bytes)
            conn_info.addr[0] = ((char*)&sin_addr)[0];
            conn_info.addr[1] = ((char*)&sin_addr)[1];
            conn_info.addr[2] = ((char*)&sin_addr)[2];
            conn_info.addr[3] = ((char*)&sin_addr)[3];
        }

        if (bpf_probe_read_kernel(&sin_port, sizeof(sin_port), &addr_in->sin_port) == 0) {
            conn_info.port = __builtin_bswap16(sin_port);
        }

        bpf_map_update_elem(&udp_dns_connections, &sk, &conn_info, BPF_ANY);

        char comm[16];
        __u64 pid_tgid = bpf_get_current_pid_tgid();
        bpf_get_current_comm(&comm, sizeof(comm));
        bpf_printk("UDP DNS connect from %s (PID: %u)", comm, pid_tgid & 0xffffffff);
    }

    /* Allow the operation to proceed */
    return 0;
}
