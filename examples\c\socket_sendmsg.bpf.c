// SPDX-License-Identifier: GPL-2.0 OR BSD-3-Clause
#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>
#include <bpf/bpf_core_read.h>
#include "socket_sendmsg.h"

char LICENSE[] SEC("license") = "GPL";

struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, 256 * 1024);
} rb SEC(".maps");

static __always_inline int extract_sockaddr_info(struct sockaddr *addr, 
                                                 struct sendmsg_event *event)
{
    if (!addr)
        return 0;

    __u16 family;
    if (bpf_probe_read_kernel(&family, sizeof(family), &addr->sa_family) != 0)
        return 0;

    event->family = family;

    if (family == AF_INET) {
        struct sockaddr_in *addr_in = (struct sockaddr_in *)addr;
        __u32 sin_addr;
        __u16 sin_port;
        
        if (bpf_probe_read_kernel(&sin_addr, sizeof(sin_addr), &addr_in->sin_addr.s_addr) == 0) {
            __builtin_memcpy(event->addr, &sin_addr, sizeof(sin_addr));
            event->addr_len = sizeof(sin_addr);
        }
        
        if (bpf_probe_read_kernel(&sin_port, sizeof(sin_port), &addr_in->sin_port) == 0) {
            event->port = __builtin_bswap16(sin_port); // Convert from network byte order
        }
    } else if (family == AF_INET6) {
        struct sockaddr_in6 *addr_in6 = (struct sockaddr_in6 *)addr;
        struct in6_addr sin6_addr;
        __u16 sin6_port;
        
        if (bpf_probe_read_kernel(&sin6_addr, sizeof(sin6_addr), &addr_in6->sin6_addr) == 0) {
            __u32 copy_len = sizeof(sin6_addr) > MAX_ADDR_SIZE ? MAX_ADDR_SIZE : sizeof(sin6_addr);
            __builtin_memcpy(event->addr, &sin6_addr, copy_len);
            event->addr_len = copy_len;
        }
        
        if (bpf_probe_read_kernel(&sin6_port, sizeof(sin6_port), &addr_in6->sin6_port) == 0) {
            event->port = __builtin_bswap16(sin6_port); // Convert from network byte order
        }
    }

    return 0;
}

static __always_inline int extract_and_print_udp_data(struct iov_iter *iter,
                                                     struct sendmsg_event *event)
{
    if (!iter)
        return 0;

    // Try to extract data from iov_iter for UDP packets
    // We'll use a simple approach that works across kernel versions
    void *data_ptr = NULL;
    size_t data_len = 0;

    // Try to read the data pointer and length from iov_iter
    // This is a simplified approach that may work on many kernel versions
    if (bpf_probe_read_kernel(&data_ptr, sizeof(data_ptr), (void*)iter + 8) == 0 &&
        bpf_probe_read_kernel(&data_len, sizeof(data_len), (void*)iter + 16) == 0) {

        if (data_ptr && data_len > 0) {
            // Limit data size for printing
            __u32 copy_len = data_len > MAX_DATA_SIZE ? MAX_DATA_SIZE : data_len;

            // Try to read the actual data
            __u8 data_buf[MAX_DATA_SIZE];
            if (bpf_probe_read_user(data_buf, copy_len, data_ptr) == 0) {
                // Print UDP data in chunks (bpf_printk has limited output)
                bpf_printk("UDP DNS Query Data (len=%u):", copy_len);

                // Print first 16 bytes as hex
                if (copy_len >= 16) {
                    bpf_printk("Data: %02x%02x%02x%02x %02x%02x%02x%02x %02x%02x%02x%02x %02x%02x%02x%02x",
                              data_buf[0], data_buf[1], data_buf[2], data_buf[3],
                              data_buf[4], data_buf[5], data_buf[6], data_buf[7],
                              data_buf[8], data_buf[9], data_buf[10], data_buf[11],
                              data_buf[12], data_buf[13], data_buf[14], data_buf[15]);
                } else if (copy_len >= 8) {
                    bpf_printk("Data: %02x%02x%02x%02x %02x%02x%02x%02x",
                              data_buf[0], data_buf[1], data_buf[2], data_buf[3],
                              data_buf[4], data_buf[5], data_buf[6], data_buf[7]);
                } else if (copy_len >= 4) {
                    bpf_printk("Data: %02x%02x%02x%02x",
                              data_buf[0], data_buf[1], data_buf[2], data_buf[3]);
                }

                // Store data in event for userspace
                __builtin_memcpy(event->data, data_buf, copy_len);
                event->data_len = copy_len;
            }
        }
    }

    return 0;
}

SEC("lsm/socket_sendmsg")
int BPF_PROG(socket_sendmsg_monitor, struct socket *sock, struct msghdr *msg, int size, int ret)
{
    /* ret is the return value from the previous BPF program
     * or 0 if it's the first hook.
     */
    if (ret != 0)
        return ret;

    struct sendmsg_event *event;
    __u64 pid_tgid;
    char comm[16];
    bool is_udp_dns = false;

    /* Get process information first for filtering */
    pid_tgid = bpf_get_current_pid_tgid();
    bpf_get_current_comm(&comm, sizeof(comm));

    /* Check if this is a UDP socket */
    if (sock) {
        struct sock *sk = BPF_CORE_READ(sock, sk);
        if (sk) {
            __u16 family = BPF_CORE_READ(sk, __sk_common.skc_family);
            __u8 protocol = BPF_CORE_READ(sk, sk_protocol);

            // Only process UDP packets
            if (protocol == IPPROTO_UDP) {
                /* Extract address information from msghdr to check port */
                if (msg) {
                    struct sockaddr *msg_name;
                    if (bpf_probe_read_kernel(&msg_name, sizeof(msg_name), &msg->msg_name) == 0 && msg_name) {
                        __u16 addr_family;
                        if (bpf_probe_read_kernel(&addr_family, sizeof(addr_family), &msg_name->sa_family) == 0) {
                            if (addr_family == AF_INET) {
                                struct sockaddr_in *addr_in = (struct sockaddr_in *)msg_name;
                                __u16 port;
                                if (bpf_probe_read_kernel(&port, sizeof(port), &addr_in->sin_port) == 0) {
                                    // Check if destination port is 53 (DNS)
                                    if (__builtin_bswap16(port) == 53) {
                                        is_udp_dns = true;
                                        bpf_printk("UDP DNS Query detected from %s (PID: %u)", comm, pid_tgid & 0xffffffff);
                                    }
                                }
                            } else if (addr_family == AF_INET6) {
                                struct sockaddr_in6 *addr_in6 = (struct sockaddr_in6 *)msg_name;
                                __u16 port;
                                if (bpf_probe_read_kernel(&port, sizeof(port), &addr_in6->sin6_port) == 0) {
                                    // Check if destination port is 53 (DNS)
                                    if (__builtin_bswap16(port) == 53) {
                                        is_udp_dns = true;
                                        bpf_printk("UDP DNS Query detected from %s (PID: %u)", comm, pid_tgid & 0xffffffff);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /* Only process UDP DNS packets (port 53) */
    if (!is_udp_dns)
        return 0;

    /* Reserve space in ring buffer */
    event = bpf_ringbuf_reserve(&rb, sizeof(*event), 0);
    if (!event)
        return 0;

    /* Initialize event structure */
    __builtin_memset(event, 0, sizeof(*event));

    /* Fill process information */
    event->pid = pid_tgid & 0xffffffff;
    event->tgid = pid_tgid >> 32;
    __builtin_memcpy(event->comm, comm, sizeof(comm));

    /* Extract address and data information from msghdr */
    if (msg) {
        struct sockaddr *msg_name;
        struct iov_iter msg_iter;

        if (bpf_probe_read_kernel(&msg_name, sizeof(msg_name), &msg->msg_name) == 0) {
            extract_sockaddr_info(msg_name, event);
        }

        if (bpf_probe_read_kernel(&msg_iter, sizeof(msg_iter), &msg->msg_iter) == 0) {
            extract_and_print_udp_data(&msg_iter, event);
        }
    }

    /* Submit event to user space */
    bpf_ringbuf_submit(event, 0);

    /* Allow the operation to proceed */
    return 0;
}
