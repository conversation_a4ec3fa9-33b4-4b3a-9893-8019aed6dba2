// SPDX-License-Identifier: GPL-2.0 OR BSD-3-Clause
#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>
#include <bpf/bpf_core_read.h>
#include "socket_sendmsg.h"

char LICENSE[] SEC("license") = "GPL";

struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, 256 * 1024);
} rb SEC(".maps");

static __always_inline int extract_sockaddr_info(struct sockaddr *addr, 
                                                 struct sendmsg_event *event)
{
    if (!addr)
        return 0;

    __u16 family;
    if (bpf_probe_read_kernel(&family, sizeof(family), &addr->sa_family) != 0)
        return 0;

    event->family = family;

    if (family == AF_INET) {
        struct sockaddr_in *addr_in = (struct sockaddr_in *)addr;
        __u32 sin_addr;
        __u16 sin_port;
        
        if (bpf_probe_read_kernel(&sin_addr, sizeof(sin_addr), &addr_in->sin_addr.s_addr) == 0) {
            __builtin_memcpy(event->addr, &sin_addr, sizeof(sin_addr));
            event->addr_len = sizeof(sin_addr);
        }
        
        if (bpf_probe_read_kernel(&sin_port, sizeof(sin_port), &addr_in->sin_port) == 0) {
            event->port = __builtin_bswap16(sin_port); // Convert from network byte order
        }
    } else if (family == AF_INET6) {
        struct sockaddr_in6 *addr_in6 = (struct sockaddr_in6 *)addr;
        struct in6_addr sin6_addr;
        __u16 sin6_port;
        
        if (bpf_probe_read_kernel(&sin6_addr, sizeof(sin6_addr), &addr_in6->sin6_addr) == 0) {
            __u32 copy_len = sizeof(sin6_addr) > MAX_ADDR_SIZE ? MAX_ADDR_SIZE : sizeof(sin6_addr);
            __builtin_memcpy(event->addr, &sin6_addr, copy_len);
            event->addr_len = copy_len;
        }
        
        if (bpf_probe_read_kernel(&sin6_port, sizeof(sin6_port), &addr_in6->sin6_port) == 0) {
            event->port = __builtin_bswap16(sin6_port); // Convert from network byte order
        }
    }

    return 0;
}

static __always_inline int extract_iovec_data(struct iovec *iov, 
                                              unsigned long iovlen,
                                              struct sendmsg_event *event)
{
    if (!iov || iovlen == 0)
        return 0;

    // Read first iovec entry
    struct iovec first_iov;
    if (bpf_probe_read_kernel(&first_iov, sizeof(first_iov), iov) != 0)
        return 0;

    if (!first_iov.iov_base || first_iov.iov_len == 0)
        return 0;

    // Copy data from the first iovec
    __u32 copy_len = first_iov.iov_len > MAX_DATA_SIZE ? MAX_DATA_SIZE : first_iov.iov_len;
    if (bpf_probe_read_user(event->data, copy_len, first_iov.iov_base) == 0) {
        event->data_len = copy_len;
    }

    return 0;
}

SEC("lsm/socket_sendmsg")
int BPF_PROG(socket_sendmsg_monitor, struct socket *sock, struct msghdr *msg, int size, int ret)
{
    /* ret is the return value from the previous BPF program
     * or 0 if it's the first hook.
     */
    if (ret != 0)
        return ret;

    struct sendmsg_event *event;
    __u64 pid_tgid;

    /* Reserve space in ring buffer */
    event = bpf_ringbuf_reserve(&rb, sizeof(*event), 0);
    if (!event)
        return 0;

    /* Initialize event structure */
    __builtin_memset(event, 0, sizeof(*event));

    /* Get process information */
    pid_tgid = bpf_get_current_pid_tgid();
    event->pid = pid_tgid & 0xffffffff;
    event->tgid = pid_tgid >> 32;
    bpf_get_current_comm(&event->comm, sizeof(event->comm));

    /* Extract address information from msghdr */
    if (msg) {
        struct sockaddr *msg_name;
        struct iovec *msg_iov;
        unsigned long msg_iovlen;

        if (bpf_probe_read_kernel(&msg_name, sizeof(msg_name), &msg->msg_name) == 0) {
            extract_sockaddr_info(msg_name, event);
        }

        if (bpf_probe_read_kernel(&msg_iov, sizeof(msg_iov), &msg->msg_iov) == 0 &&
            bpf_probe_read_kernel(&msg_iovlen, sizeof(msg_iovlen), &msg->msg_iovlen) == 0) {
            extract_iovec_data(msg_iov, msg_iovlen, event);
        }
    }

    /* Submit event to user space */
    bpf_ringbuf_submit(event, 0);

    /* Allow the operation to proceed */
    return 0;
}
