#!/bin/bash

echo "Testing UDP DNS monitoring program..."
echo "1. Building the program..."
make socket_sendmsg

if [ $? -eq 0 ]; then
    echo "2. Build successful!"
    echo "3. To test the program:"
    echo "   - Run: sudo ./socket_sendmsg"
    echo "   - In another terminal, run: sudo cat /sys/kernel/debug/tracing/trace_pipe"
    echo "   - Generate DNS queries: nslookup google.com"
    echo "   - You should see UDP DNS queries being logged"
else
    echo "2. Build failed. Please check the compilation errors above."
fi
