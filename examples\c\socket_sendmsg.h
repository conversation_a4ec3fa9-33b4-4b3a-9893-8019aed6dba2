/* SPDX-License-Identifier: (LGPL-2.1 OR BSD-2-Clause) */
#ifndef __SOCKET_SENDMSG_H
#define __SOCKET_SENDMSG_H

#define MAX_DATA_SIZE 64
#define MAX_ADDR_SIZE 16

struct sendmsg_event {
    __u32 pid;
    __u32 tgid;
    __u16 family;
    __u16 port;
    __u32 addr_len;
    __u32 data_len;
    __u8 addr[MAX_ADDR_SIZE];
    __u8 data[MAX_DATA_SIZE];
    char comm[16];
};

#endif /* __SOCKET_SENDMSG_H */
